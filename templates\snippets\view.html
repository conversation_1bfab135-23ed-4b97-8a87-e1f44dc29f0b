{% extends "layout.html" %}

{% block title %}{{ snippet.title }} - <PERSON>lask Auth{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center">
      <a href="{{ url_for('auth.snippets') }}" 
         class="text-[var(--accent-blue)] hover:underline mr-4">
        <i class="fa-solid fa-arrow-left mr-2"></i>Back to Snippets
      </a>
      <h1 class="text-3xl font-bold custom-logo">{{ snippet.title }}</h1>
    </div>
    
    {% if current_user.is_authenticated and current_user.id == snippet.user_id %}
      <div class="flex space-x-2">
        <a href="{{ url_for('auth.edit_snippet', id=snippet.id) }}" 
           class="custom-btn-secondary px-4 py-2 rounded-lg">
          <i class="fa-solid fa-edit mr-2"></i>Edit
        </a>
        <form method="POST" action="{{ url_for('auth.delete_snippet', id=snippet.id) }}" 
              class="inline" onsubmit="return confirm('Are you sure you want to delete this snippet?')">
          <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
            <i class="fa-solid fa-trash mr-2"></i>Delete
          </button>
        </form>
      </div>
    {% endif %}
  </div>

  <!-- Snippet Info -->
  <div class="bg-[var(--bg-card)] rounded-lg p-6 mb-6 shadow-lg border border-[var(--bg-card-alt)]">
    <div class="flex flex-wrap items-center justify-between mb-4">
      <div class="flex items-center space-x-4">
        <span class="flex items-center text-sm text-gray-600">
          <i class="fa-solid fa-user mr-2"></i>{{ snippet.author.username }}
        </span>
        <span class="flex items-center text-sm text-gray-600">
          <i class="fa-solid fa-clock mr-2"></i>{{ snippet.created_at.strftime('%B %d, %Y at %I:%M %p') }}
        </span>
        {% if snippet.updated_at != snippet.created_at %}
          <span class="flex items-center text-sm text-gray-600">
            <i class="fa-solid fa-edit mr-2"></i>Updated {{ snippet.updated_at.strftime('%B %d, %Y at %I:%M %p') }}
          </span>
        {% endif %}
      </div>
      
      <div class="flex items-center space-x-2">
        <span class="px-3 py-1 text-sm rounded-full {{ 'bg-green-100 text-green-800' if snippet.is_public else 'bg-gray-100 text-gray-800' }}">
          <i class="fa-solid {{ 'fa-globe' if snippet.is_public else 'fa-lock' }} mr-1"></i>
          {{ snippet.visibility_text }}
        </span>
        {% if snippet.language != 'text' %}
          <span class="px-3 py-1 text-sm bg-[var(--accent-purple)]/20 text-[var(--accent-purple)] rounded-full">
            <i class="fa-solid fa-code mr-1"></i>{{ snippet.language }}
          </span>
        {% endif %}
      </div>
    </div>
    
    {% if snippet.description %}
      <div class="border-t border-[var(--bg-card-alt)] pt-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">Description</h3>
        <p class="text-gray-600">{{ snippet.description }}</p>
      </div>
    {% endif %}
  </div>

  <!-- Code Display -->
  <div class="bg-[var(--bg-card)] rounded-lg shadow-lg border border-[var(--bg-card-alt)] overflow-hidden">
    <div class="flex items-center justify-between px-6 py-3 bg-[var(--bg-card-alt)] border-b border-[var(--bg-card-alt)]">
      <h3 class="text-lg font-semibold">Code</h3>
      <button onclick="copyCode()" 
              class="flex items-center px-3 py-1 text-sm bg-[var(--accent-blue)] text-white rounded hover:bg-[var(--accent-blue)]/80 transition-colors">
        <i class="fa-solid fa-copy mr-2"></i>Copy
      </button>
    </div>
    
    <div class="relative">
      <pre id="code-block" class="language-{{ snippet.language }} line-numbers p-6 overflow-x-auto text-sm" style="background: #2d3748; border-radius: 0 0 0.5rem 0.5rem;"><code class="language-{{ snippet.language }}">{{ snippet.code }}</code></pre>
    </div>
  </div>

  <!-- Actions for authenticated users -->
  {% if current_user.is_authenticated %}
    <div class="mt-6 text-center">
      <a href="{{ url_for('auth.create_snippet') }}" 
         class="custom-btn-primary inline-flex items-center px-6 py-3 rounded-lg">
        <i class="fa-solid fa-plus mr-2"></i>Create New Snippet
      </a>
    </div>
  {% endif %}
</div>

<script>
function copyCode() {
  const codeBlock = document.getElementById('code-block');
  const text = codeBlock.textContent || codeBlock.innerText;
  
  navigator.clipboard.writeText(text).then(function() {
    // Show success feedback
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fa-solid fa-check mr-2"></i>Copied!';
    button.classList.add('bg-green-500');
    button.classList.remove('bg-[var(--accent-blue)]');
    
    setTimeout(function() {
      button.innerHTML = originalText;
      button.classList.remove('bg-green-500');
      button.classList.add('bg-[var(--accent-blue)]');
    }, 2000);
  }).catch(function(err) {
    console.error('Could not copy text: ', err);
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      const button = event.target.closest('button');
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fa-solid fa-check mr-2"></i>Copied!';
      button.classList.add('bg-green-500');
      button.classList.remove('bg-[var(--accent-blue)]');
      
      setTimeout(function() {
        button.innerHTML = originalText;
        button.classList.remove('bg-green-500');
        button.classList.add('bg-[var(--accent-blue)]');
      }, 2000);
    } catch (err) {
      console.error('Fallback copy failed: ', err);
    }
    document.body.removeChild(textArea);
  });
}
</script>
{% endblock %}
