#!/usr/bin/env python3
"""
Database migration script to handle profile_pic column changes
"""

import sqlite3
import os
from app import create_app
from extensions import db

def migrate_database():
    """Migrate the database to handle profile_pic column changes"""
    
    app = create_app()
    
    with app.app_context():
        # Get the database path
        db_path = app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')
        
        if not os.path.exists(db_path):
            print("Database doesn't exist yet. Creating new database...")
            db.create_all()
            print("✅ New database created successfully!")
            return
        
        print(f"Migrating database at: {db_path}")
        
        # Connect directly to SQLite to perform migration
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # Check if we need to migrate the user table
            cursor.execute("PRAGMA table_info(user)")
            columns = cursor.fetchall()
            
            profile_pic_column = None
            for column in columns:
                if column[1] == 'profile_pic':
                    profile_pic_column = column
                    break
            
            if profile_pic_column and profile_pic_column[3] == 1:  # NOT NULL constraint exists
                print("🔄 Migrating user table to allow NULL profile_pic...")
                
                # Create new table with correct schema
                cursor.execute('''
                    CREATE TABLE user_new (
                        id INTEGER PRIMARY KEY,
                        username VARCHAR(64) UNIQUE NOT NULL,
                        email VARCHAR(120) UNIQUE NOT NULL,
                        password VARCHAR(200) NOT NULL,
                        profile_pic VARCHAR(120)
                    )
                ''')
                
                # Copy data from old table, converting 'default.jpg' to NULL
                cursor.execute('''
                    INSERT INTO user_new (id, username, email, password, profile_pic)
                    SELECT id, username, email, password, 
                           CASE WHEN profile_pic = 'default.jpg' THEN NULL ELSE profile_pic END
                    FROM user
                ''')
                
                # Drop old table and rename new one
                cursor.execute('DROP TABLE user')
                cursor.execute('ALTER TABLE user_new RENAME TO user')
                
                print("✅ User table migrated successfully!")
            else:
                print("✅ User table already has correct schema!")
            
            # Check if code_snippet table exists, if not create it
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='code_snippet'")
            if not cursor.fetchone():
                print("🔄 Creating code_snippet table...")
                cursor.execute('''
                    CREATE TABLE code_snippet (
                        id INTEGER PRIMARY KEY,
                        title VARCHAR(100) NOT NULL,
                        description TEXT,
                        code TEXT NOT NULL,
                        language VARCHAR(50) NOT NULL DEFAULT 'text',
                        is_public BOOLEAN NOT NULL DEFAULT 1,
                        created_at DATETIME NOT NULL,
                        updated_at DATETIME NOT NULL,
                        user_id INTEGER NOT NULL,
                        FOREIGN KEY (user_id) REFERENCES user(id)
                    )
                ''')
                print("✅ Code snippet table created successfully!")
            else:
                print("✅ Code snippet table already exists!")
            
            conn.commit()
            print("🎉 Database migration completed successfully!")
            
        except Exception as e:
            conn.rollback()
            print(f"❌ Migration failed: {e}")
            raise
        finally:
            conn.close()

if __name__ == '__main__':
    migrate_database()
