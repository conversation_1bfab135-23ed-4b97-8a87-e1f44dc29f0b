{% extends "layout.html" %}

{% block title %}My Snippets - Flask Auth{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold custom-logo">
      <i class="fa-solid fa-user mr-3"></i>My Code Snippets
    </h1>
    <a href="{{ url_for('auth.create_snippet') }}" 
       class="custom-btn-primary inline-flex items-center px-4 py-2 rounded-lg">
      <i class="fa-solid fa-plus mr-2"></i>New Snippet
    </a>
  </div>

  {% if snippets.items %}
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {% for snippet in snippets.items %}
        <div class="bg-[var(--bg-card)] rounded-lg p-6 shadow-lg border border-[var(--bg-card-alt)] hover:shadow-xl transition-all duration-200">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-semibold text-[var(--accent-blue)] truncate">
              <a href="{{ url_for('auth.view_snippet', id=snippet.id) }}" class="hover:underline">
                {{ snippet.title }}
              </a>
            </h3>
            <div class="flex items-center space-x-2 ml-2">
              <span class="px-2 py-1 text-xs rounded-full {{ 'bg-green-100 text-green-800' if snippet.is_public else 'bg-gray-100 text-gray-800' }}">
                {{ snippet.visibility_text }}
              </span>
              {% if snippet.language != 'text' %}
                <span class="px-2 py-1 text-xs bg-[var(--accent-purple)]/20 text-[var(--accent-purple)] rounded-full">
                  {{ snippet.language }}
                </span>
              {% endif %}
            </div>
          </div>
          
          {% if snippet.description %}
            <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ snippet.description }}</p>
          {% endif %}
          
          <div class="bg-[var(--bg-main)] rounded p-3 mb-3 overflow-hidden">
            <pre class="text-sm text-gray-700 whitespace-pre-wrap line-clamp-3">{{ snippet.code[:100] }}{% if snippet.code|length > 100 %}...{% endif %}</pre>
          </div>
          
          <div class="flex justify-between items-center text-xs text-gray-500 mb-3">
            <span>
              <i class="fa-solid fa-clock mr-1"></i>{{ snippet.created_at.strftime('%b %d, %Y') }}
            </span>
            {% if snippet.updated_at != snippet.created_at %}
              <span>
                <i class="fa-solid fa-edit mr-1"></i>Updated {{ snippet.updated_at.strftime('%b %d') }}
              </span>
            {% endif %}
          </div>
          
          <div class="pt-3 border-t border-[var(--bg-card-alt)] flex justify-between">
            <div class="flex space-x-2">
              <a href="{{ url_for('auth.edit_snippet', id=snippet.id) }}" 
                 class="text-[var(--accent-blue)] hover:underline text-sm">
                <i class="fa-solid fa-edit mr-1"></i>Edit
              </a>
              <a href="{{ url_for('auth.view_snippet', id=snippet.id) }}" 
                 class="text-[var(--accent-purple)] hover:underline text-sm">
                <i class="fa-solid fa-eye mr-1"></i>View
              </a>
            </div>
            <form method="POST" action="{{ url_for('auth.delete_snippet', id=snippet.id) }}" 
                  class="inline" onsubmit="return confirm('Are you sure you want to delete this snippet?')">
              <button type="submit" class="text-red-500 hover:underline text-sm">
                <i class="fa-solid fa-trash mr-1"></i>Delete
              </button>
            </form>
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Pagination -->
    {% if snippets.pages > 1 %}
      <div class="mt-8 flex justify-center">
        <nav class="flex space-x-2">
          {% if snippets.has_prev %}
            <a href="{{ url_for('auth.my_snippets', page=snippets.prev_num) }}" 
               class="px-3 py-2 bg-[var(--bg-card)] text-[var(--accent-blue)] rounded hover:bg-[var(--bg-card-alt)] transition-colors">
              <i class="fa-solid fa-chevron-left"></i>
            </a>
          {% endif %}
          
          {% for page_num in snippets.iter_pages() %}
            {% if page_num %}
              {% if page_num != snippets.page %}
                <a href="{{ url_for('auth.my_snippets', page=page_num) }}" 
                   class="px-3 py-2 bg-[var(--bg-card)] text-[var(--accent-blue)] rounded hover:bg-[var(--bg-card-alt)] transition-colors">
                  {{ page_num }}
                </a>
              {% else %}
                <span class="px-3 py-2 bg-[var(--accent-blue)] text-white rounded">{{ page_num }}</span>
              {% endif %}
            {% else %}
              <span class="px-3 py-2">...</span>
            {% endif %}
          {% endfor %}
          
          {% if snippets.has_next %}
            <a href="{{ url_for('auth.my_snippets', page=snippets.next_num) }}" 
               class="px-3 py-2 bg-[var(--bg-card)] text-[var(--accent-blue)] rounded hover:bg-[var(--bg-card-alt)] transition-colors">
              <i class="fa-solid fa-chevron-right"></i>
            </a>
          {% endif %}
        </nav>
      </div>
    {% endif %}
  {% else %}
    <div class="text-center py-12">
      <i class="fa-solid fa-code text-6xl text-gray-400 mb-4"></i>
      <h3 class="text-xl font-semibold text-gray-600 mb-2">You haven't created any snippets yet</h3>
      <p class="text-gray-500 mb-6">Start sharing your code with the community!</p>
      <a href="{{ url_for('auth.create_snippet') }}" 
         class="custom-btn-primary inline-flex items-center px-6 py-3 rounded-lg">
        <i class="fa-solid fa-plus mr-2"></i>Create Your First Snippet
      </a>
    </div>
  {% endif %}
</div>

<style>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
{% endblock %}
