{% extends "layout.html" %} {% block title %}Set New Password{% endblock %} {%
block content %}
<div class="w-full">
  <h2 class="text-3xl font-semibold mb-8 text-center">Set a New Password</h2>

  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 md:gap-6">
    <div
      class="md:col-span-1 bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg flex flex-col items-center justify-center"
    >
      <div
        class="text-4xl md:text-5xl mb-4 text-transparent bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] bg-clip-text"
      >
        <i class="fa-solid fa-lock-open"></i>
      </div>
      <h3 class="text-xl mb-2 text-center">Create New Password</h3>
      <p class="text-[var(--text-secondary)] text-sm text-center">
        Choose a strong, secure password for your account.
      </p>
    </div>

    <div
      class="md:col-span-3 bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg"
    >
      <form method="POST" class="space-y-4 md:space-y-6 w-full">
        {{ form.hidden_tag() }}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div class="w-full">
            {{ form.password.label(class_='block mb-1 md:mb-2 text-sm') }}
            <div class="relative w-full">
              <span
                class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]"
              >
                <i class="fa-solid fa-lock"></i>
              </span>
              {{ form.password(class_='w-full pl-10', placeholder='Enter new
              password') }}
              <button
                type="button"
                class="password-toggle-btn"
                aria-label="Toggle password visibility"
              >
                <i class="fa-solid fa-eye"></i>
              </button>
            </div>
          </div>

          <div class="w-full">
            {{ form.confirm_password.label(class_='block mb-1 md:mb-2 text-sm')
            }}
            <div class="relative w-full">
              <span
                class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]"
              >
                <i class="fa-solid fa-shield-halved"></i>
              </span>
              {{ form.confirm_password(class_='w-full pl-10',
              placeholder='Confirm new password') }}
              <button
                type="button"
                class="password-toggle-btn"
                aria-label="Toggle password visibility"
              >
                <i class="fa-solid fa-eye"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="mt-6 md:mt-8">
          <button type="submit" class="w-full flex items-center justify-center">
            <i class="fa-solid fa-check mr-2"></i>
            <span>Reset Password</span>
          </button>
        </div>

        <div class="mt-4 md:mt-6 text-center text-sm">
          <a href="{{ url_for('auth.login') }}" class="custom-link">
            <i class="fa-solid fa-arrow-left mr-1"></i> Back to Login
          </a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
