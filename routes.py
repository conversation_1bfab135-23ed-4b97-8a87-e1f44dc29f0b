from flask import Blueprint, render_template, redirect, url_for, flash, request
from forms import RegisterForm, LoginForm, RequestResetForm, ResetPasswordForm, ProfileUpdateForm
import os
from werkzeug.utils import secure_filename
from models import User
from extensions import db, bcrypt
from flask_login import login_user, logout_user, login_required, current_user

bp = Blueprint('auth', __name__)

@bp.route("/")
def home():
    return render_template("home.html")

@bp.route("/register", methods=["GET", "POST"])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    form = RegisterForm()
    if form.validate_on_submit():
        hashed_pw = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
        user = User(username=form.username.data, email=form.email.data, password=hashed_pw)
        db.session.add(user)
        db.session.commit()
        flash("Account created! You can now log in.", "success")
        return redirect(url_for("auth.login"))
    return render_template("register.html", form=form)

@bp.route("/login", methods=["GET", "POST"])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user and bcrypt.check_password_hash(user.password, form.password.data):
            login_user(user, remember=form.remember.data)
            return redirect(url_for('auth.home'))
        else:
            flash("Login failed. Check email and password.", "danger")
    return render_template("login.html", form=form)

@bp.route("/logout")
def logout():
    logout_user()
    return redirect(url_for("auth.home"))

@bp.route("/reset_password", methods=["GET", "POST"])
def reset_request():
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    form = RequestResetForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        token = user.get_reset_token()
        reset_url = url_for("auth.reset_token", token=token, _external=True)
        print(f"[SIMULATED EMAIL] Reset link: {reset_url}")  # Simulate email
        flash("Reset instructions sent! (Check console for token URL)", "info")
        return redirect(url_for("auth.login"))
    return render_template("reset_request.html", form=form)

@bp.route("/reset_password/<token>", methods=["GET", "POST"])
def reset_token(token):
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    user = User.verify_reset_token(token)
    if not user:
        flash("Invalid or expired token", "warning")
        return redirect(url_for("auth.reset_request"))
    form = ResetPasswordForm()
    if form.validate_on_submit():
        hashed_pw = bcrypt.generate_password_hash(form.password.data).decode("utf-8")
        user.password = hashed_pw
        db.session.commit()
        flash("Your password has been updated!", "success")
        return redirect(url_for("auth.login"))
    return render_template("reset_token.html", form=form)

@bp.route("/dashboard", methods=["GET", "POST"])
@login_required
def dashboard():
    form = ProfileUpdateForm(obj=current_user)
    # Set user_id attribute for custom validation
    form.user_id = current_user.id
    
    if form.validate_on_submit():
        # Update username and email
        current_user.username = form.username.data
        current_user.email = form.email.data
        
        # Update password only if provided
        if form.password.data and form.password.data.strip():
            current_user.password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
        
        # Handle profile picture upload
        if form.picture.data:
            # Create directory if it doesn't exist
            profile_pics_dir = os.path.join('static', 'profile_pics')
            if not os.path.exists(profile_pics_dir):
                os.makedirs(profile_pics_dir)
                
            pic_file = secure_filename(form.picture.data.filename)
            pic_path = os.path.join(profile_pics_dir, pic_file)
            form.picture.data.save(pic_path)
            
            # Delete old profile picture if it's not the default
            if current_user.profile_pic != 'default.jpg':
                old_pic_path = os.path.join(profile_pics_dir, current_user.profile_pic)
                if os.path.exists(old_pic_path):
                    os.remove(old_pic_path)
                    
            current_user.profile_pic = pic_file
            
        db.session.commit()
        flash('Profile updated successfully!', 'success')
        return redirect(url_for('auth.dashboard'))
        
    # Pre-fill form with current user data
    form.username.data = current_user.username
    form.email.data = current_user.email
    # Don't pre-fill password fields
    form.password.data = ''
    form.confirm_password.data = ''
    
    return render_template('dashboard.html', form=form, user=current_user)

@bp.route("/delete_profile_pic", methods=["POST"])
@login_required
def delete_profile_pic():
    if current_user.profile_pic != 'default.jpg':
        # Delete the profile picture file
        profile_pics_dir = os.path.join('static', 'profile_pics')
        pic_path = os.path.join(profile_pics_dir, current_user.profile_pic)
        if os.path.exists(pic_path):
            os.remove(pic_path)
        
        # Reset to default
        current_user.profile_pic = 'default.jpg'
        db.session.commit()
        flash('Profile picture removed successfully!', 'success')
    else:
        flash('No profile picture to delete.', 'info')
    
    return redirect(url_for('auth.dashboard'))
