from flask import Blueprint, render_template, redirect, url_for, flash, request, abort
from forms import RegisterForm, LoginForm, RequestResetForm, ResetPasswordForm, ProfileUpdateForm, CodeSnippetForm, EditSnippetForm
import os
from werkzeug.utils import secure_filename
from models import User, CodeSnippet
from extensions import db, bcrypt
from flask_login import login_user, logout_user, login_required, current_user

bp = Blueprint('auth', __name__)

@bp.route("/")
def home():
    return render_template("home.html")

@bp.route("/register", methods=["GET", "POST"])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    form = RegisterForm()
    if form.validate_on_submit():
        hashed_pw = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
        user = User(username=form.username.data, email=form.email.data, password=hashed_pw)
        db.session.add(user)
        db.session.commit()
        flash("Account created! You can now log in.", "success")
        return redirect(url_for("auth.login"))
    return render_template("register.html", form=form)

@bp.route("/login", methods=["GET", "POST"])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user and bcrypt.check_password_hash(user.password, form.password.data):
            login_user(user, remember=form.remember.data)
            return redirect(url_for('auth.home'))
        else:
            flash("Login failed. Check email and password.", "danger")
    return render_template("login.html", form=form)

@bp.route("/logout")
def logout():
    logout_user()
    return redirect(url_for("auth.home"))

@bp.route("/reset_password", methods=["GET", "POST"])
def reset_request():
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    form = RequestResetForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        token = user.get_reset_token()
        reset_url = url_for("auth.reset_token", token=token, _external=True)
        print(f"[SIMULATED EMAIL] Reset link: {reset_url}")  # Simulate email
        flash("Reset instructions sent! (Check console for token URL)", "info")
        return redirect(url_for("auth.login"))
    return render_template("reset_request.html", form=form)

@bp.route("/reset_password/<token>", methods=["GET", "POST"])
def reset_token(token):
    if current_user.is_authenticated:
        return redirect(url_for('auth.home'))
    user = User.verify_reset_token(token)
    if not user:
        flash("Invalid or expired token", "warning")
        return redirect(url_for("auth.reset_request"))
    form = ResetPasswordForm()
    if form.validate_on_submit():
        hashed_pw = bcrypt.generate_password_hash(form.password.data).decode("utf-8")
        user.password = hashed_pw
        db.session.commit()
        flash("Your password has been updated!", "success")
        return redirect(url_for("auth.login"))
    return render_template("reset_token.html", form=form)

@bp.route("/dashboard", methods=["GET", "POST"])
@login_required
def dashboard():
    form = ProfileUpdateForm(obj=current_user)
    # Set user_id attribute for custom validation
    form.user_id = current_user.id
    
    if form.validate_on_submit():
        # Update username and email
        current_user.username = form.username.data
        current_user.email = form.email.data
        
        # Update password only if provided
        if form.password.data and form.password.data.strip():
            current_user.password = bcrypt.generate_password_hash(form.password.data).decode('utf-8')
        
        # Handle profile picture upload
        if form.picture.data:
            # Create directory if it doesn't exist
            profile_pics_dir = os.path.join('static', 'profile_pics')
            if not os.path.exists(profile_pics_dir):
                os.makedirs(profile_pics_dir)
                
            pic_file = secure_filename(form.picture.data.filename)
            pic_path = os.path.join(profile_pics_dir, pic_file)
            form.picture.data.save(pic_path)
            
            # Delete old profile picture if it's not the default
            if current_user.profile_pic != 'default.jpg':
                old_pic_path = os.path.join(profile_pics_dir, current_user.profile_pic)
                if os.path.exists(old_pic_path):
                    os.remove(old_pic_path)
                    
            current_user.profile_pic = pic_file
            
        db.session.commit()
        flash('Profile updated successfully!', 'success')
        return redirect(url_for('auth.dashboard'))
        
    # Pre-fill form with current user data
    form.username.data = current_user.username
    form.email.data = current_user.email
    # Don't pre-fill password fields
    form.password.data = ''
    form.confirm_password.data = ''
    
    return render_template('dashboard.html', form=form, user=current_user)

@bp.route("/delete_profile_pic", methods=["POST"])
@login_required
def delete_profile_pic():
    if current_user.profile_pic != 'default.jpg':
        # Delete the profile picture file
        profile_pics_dir = os.path.join('static', 'profile_pics')
        pic_path = os.path.join(profile_pics_dir, current_user.profile_pic)
        if os.path.exists(pic_path):
            os.remove(pic_path)
        
        # Reset to default
        current_user.profile_pic = 'default.jpg'
        db.session.commit()
        flash('Profile picture removed successfully!', 'success')
    else:
        flash('No profile picture to delete.', 'info')
    
    return redirect(url_for('auth.dashboard'))


# Code Snippet Routes
@bp.route("/snippets")
def snippets():
    """Display all public snippets and user's own snippets"""
    page = request.args.get('page', 1, type=int)

    if current_user.is_authenticated:
        # Show public snippets and user's own snippets
        snippets = CodeSnippet.query.filter(
            (CodeSnippet.is_public == True) | (CodeSnippet.user_id == current_user.id)
        ).order_by(CodeSnippet.created_at.desc()).paginate(
            page=page, per_page=10, error_out=False
        )
    else:
        # Show only public snippets for anonymous users
        snippets = CodeSnippet.query.filter_by(is_public=True).order_by(
            CodeSnippet.created_at.desc()
        ).paginate(page=page, per_page=10, error_out=False)

    return render_template('snippets/list.html', snippets=snippets)


@bp.route("/snippets/create", methods=["GET", "POST"])
@login_required
def create_snippet():
    """Create a new code snippet"""
    form = CodeSnippetForm()
    if form.validate_on_submit():
        snippet = CodeSnippet(
            title=form.title.data,
            description=form.description.data,
            code=form.code.data,
            language=form.language.data,
            is_public=form.is_public.data,
            user_id=current_user.id
        )
        db.session.add(snippet)
        db.session.commit()
        flash('Code snippet created successfully!', 'success')
        return redirect(url_for('auth.view_snippet', id=snippet.id))

    return render_template('snippets/create.html', form=form)


@bp.route("/snippets/<int:id>")
def view_snippet(id):
    """View a specific code snippet"""
    snippet = CodeSnippet.query.get_or_404(id)

    # Check if user can view this snippet
    if not snippet.is_public and (not current_user.is_authenticated or snippet.user_id != current_user.id):
        abort(403)

    return render_template('snippets/view.html', snippet=snippet)


@bp.route("/snippets/<int:id>/edit", methods=["GET", "POST"])
@login_required
def edit_snippet(id):
    """Edit a code snippet"""
    snippet = CodeSnippet.query.get_or_404(id)

    # Check if user owns this snippet
    if snippet.user_id != current_user.id:
        abort(403)

    form = EditSnippetForm(obj=snippet)
    if form.validate_on_submit():
        snippet.title = form.title.data
        snippet.description = form.description.data
        snippet.code = form.code.data
        snippet.language = form.language.data
        snippet.is_public = form.is_public.data
        db.session.commit()
        flash('Code snippet updated successfully!', 'success')
        return redirect(url_for('auth.view_snippet', id=snippet.id))

    return render_template('snippets/edit.html', form=form, snippet=snippet)


@bp.route("/snippets/<int:id>/delete", methods=["POST"])
@login_required
def delete_snippet(id):
    """Delete a code snippet"""
    snippet = CodeSnippet.query.get_or_404(id)

    # Check if user owns this snippet
    if snippet.user_id != current_user.id:
        abort(403)

    db.session.delete(snippet)
    db.session.commit()
    flash('Code snippet deleted successfully!', 'success')
    return redirect(url_for('auth.my_snippets'))


@bp.route("/my-snippets")
@login_required
def my_snippets():
    """Display current user's snippets"""
    page = request.args.get('page', 1, type=int)
    snippets = CodeSnippet.query.filter_by(user_id=current_user.id).order_by(
        CodeSnippet.created_at.desc()
    ).paginate(page=page, per_page=10, error_out=False)

    return render_template('snippets/my_snippets.html', snippets=snippets)
