{% extends "layout.html" %}
{% block title %}Home{% endblock %}
{% block content %}
<div class="w-full">
  <!-- Hero Section -->
  <div class="bg-[var(--bg-card)]/70 p-4 md:p-8 rounded-xl border border-[var(--bg-card-alt)] shadow-lg mb-6 md:mb-10 text-center">
    <h1 class="text-3xl md:text-4xl font-semibold mb-3 md:mb-4">Welcome{% if current_user.is_authenticated %}, {{ current_user.username }}{% endif %}!</h1>
    <p class="text-lg md:text-xl mb-4 md:mb-6 text-[var(--text-primary)]">Secure authentication system built with Flask and modern design</p>
    
    {% if not current_user.is_authenticated %}
    <div class="flex flex-wrap justify-center gap-3 md:gap-4 mt-6 md:mt-8">
      <a href="{{ url_for('auth.login') }}" class="px-6 md:px-8 py-2 md:py-3 bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] text-white font-bold rounded-lg hover:shadow-lg hover:shadow-[var(--accent-blue)]/50 transition-all">
        <i class="fa-solid fa-key mr-2"></i>Login
      </a>
      <a href="{{ url_for('auth.register') }}" class="px-6 md:px-8 py-2 md:py-3 bg-transparent border-2 border-[var(--accent-blue)] text-[var(--accent-blue)] font-bold rounded-lg hover:bg-[var(--accent-blue)]/10 transition-all">
        <i class="fa-solid fa-user-plus mr-2"></i>Register
      </a>
    </div>
    {% else %}
    <div class="flex justify-center mt-6 md:mt-8">
      <a href="{{ url_for('auth.dashboard') }}" class="px-6 md:px-8 py-2 md:py-3 bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] text-white font-bold rounded-lg hover:shadow-lg hover:shadow-[var(--accent-blue)]/50 transition-all">
        <i class="fa-solid fa-user mr-2"></i>Go to Dashboard
      </a>
    </div>
    {% endif %}
  </div>
  
  <!-- Features Section -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-10">
    <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg text-center">
      <div class="text-3xl md:text-4xl mb-3 md:mb-4 text-transparent bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] bg-clip-text">
        <i class="fa-solid fa-lock"></i>
      </div>
      <h3 class="text-lg md:text-xl font-semibold mb-2">Secure Authentication</h3>
      <p class="text-[var(--text-secondary)] text-sm md:text-base">Robust login system with password hashing and protection against common vulnerabilities.</p>
    </div>
    
    <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg text-center">
      <div class="text-3xl md:text-4xl mb-3 md:mb-4 text-transparent bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] bg-clip-text">
        <i class="fa-solid fa-user"></i>
      </div>
      <h3 class="text-lg md:text-xl font-semibold mb-2">User Profiles</h3>
      <p class="text-[var(--text-secondary)] text-sm md:text-base">Personalized user dashboards with customizable profile pictures and account settings.</p>
    </div>
    
    <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg text-center">
      <div class="text-3xl md:text-4xl mb-3 md:mb-4 text-transparent bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] bg-clip-text">
        <i class="fa-solid fa-rotate"></i>
      </div>
      <h3 class="text-lg md:text-xl font-semibold mb-2">Password Recovery</h3>
      <p class="text-[var(--text-secondary)] text-sm md:text-base">Simple and secure password reset functionality to help users regain access.</p>
    </div>
    
    <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg text-center">
      <div class="text-3xl md:text-4xl mb-3 md:mb-4 text-transparent bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] bg-clip-text">
        <i class="fa-solid fa-shield-halved"></i>
      </div>
      <h3 class="text-lg md:text-xl font-semibold mb-2">Data Protection</h3>
      <p class="text-[var(--text-secondary)] text-sm md:text-base">Your data is encrypted and protected with industry-standard security practices.</p>
    </div>
  </div>
  
  <!-- Tech Stack Section -->
  <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg">
    <h3 class="text-lg md:text-xl font-semibold mb-3 md:mb-4 text-center">Built With Modern Technology</h3>
    <div class="flex flex-wrap justify-center gap-3 md:gap-4">
      <span class="px-4 py-2 bg-[var(--bg-base)] rounded-full text-sm md:text-base">
        <i class="fa-brands fa-python mr-1 text-[var(--accent-blue)]"></i>Flask
      </span>
      <span class="px-4 py-2 bg-[var(--bg-base)] rounded-full text-sm md:text-base">
        <i class="fa-solid fa-database mr-1 text-[var(--accent-blue)]"></i>SQLAlchemy
      </span>
      <span class="px-4 py-2 bg-[var(--bg-base)] rounded-full text-sm md:text-base">
        <i class="fa-brands fa-css3 mr-1 text-[var(--accent-blue)]"></i>Tailwind CSS
      </span>
      <span class="px-4 py-2 bg-[var(--bg-base)] rounded-full text-sm md:text-base">
        <i class="fa-solid fa-user-shield mr-1 text-[var(--accent-blue)]"></i>Flask-Login
      </span>
      <span class="px-4 py-2 bg-[var(--bg-base)] rounded-full text-sm md:text-base">
        <i class="fa-solid fa-clipboard-check mr-1 text-[var(--accent-blue)]"></i>Flask-WTF
      </span>
    </div>
  </div>
</div>
{% endblock %}
