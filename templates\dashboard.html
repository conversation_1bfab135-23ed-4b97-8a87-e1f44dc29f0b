{% extends "layout.html" %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<div class="w-full">
  <h2 class="text-3xl font-semibold mb-8 text-center">User Dashboard</h2>
  
  <div class="flex flex-col items-center mb-10">
    {% if not user.profile_pic %}
      <div class="w-32 h-32 rounded-full flex items-center justify-center border-4 mb-4" style="background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple)); border-color: var(--accent-blue); color: #fff; font-size: 3rem; box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);">
        {{ user.username[0].upper() }}
      </div>
    {% else %}
      <div class="relative">
        <img src="{{ url_for('static', filename='profile_pics/' + user.profile_pic) }}" class="w-32 h-32 rounded-full object-cover border-4 mb-4" style="border-color: var(--accent-blue); box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);" alt="Profile Picture">
        <form method="POST" action="{{ url_for('auth.delete_profile_pic') }}" class="absolute -top-3 -right-3">
          <button type="submit" class="bg-red-600 hover:bg-red-700 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg" onclick="return confirm('Are you sure you want to delete your profile picture?');" title="Delete profile picture">
            <i class="fa-solid fa-trash-can text-sm"></i>
          </button>
        </form>
      </div>
    {% endif %}
    <span class="text-xl font-bold">{{ user.username }}</span>
    <span class="text-[var(--text-secondary)] text-sm">{{ user.email }}</span>
  </div>

  <form method="POST" enctype="multipart/form-data" class="space-y-8 w-full">
    {{ form.hidden_tag() }}
    
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
      <!-- Account Information -->
      <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg">
        <h3 class="text-xl mb-4 border-b border-[var(--bg-card-alt)] pb-2">
          <i class="fa-solid fa-user-pen mr-2"></i>Account Information
        </h3>
        <div class="space-y-4">
          <div class="w-full">
            {{ form.username.label(class_='block mb-2 text-sm') }}
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]">
                <i class="fa-solid fa-user"></i>
              </span>
              {{ form.username(class_='w-full pl-10', placeholder='Your username') }}
            </div>
            {% if form.username.errors %}
              <div class="text-red-500 text-xs mt-1">
                {% for error in form.username.errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
          <div class="w-full">
            {{ form.email.label(class_='block mb-2 text-sm') }}
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]">
                <i class="fa-solid fa-envelope"></i>
              </span>
              {{ form.email(class_='w-full pl-10', placeholder='Your email') }}
            </div>
            {% if form.email.errors %}
              <div class="text-red-500 text-xs mt-1">
                {% for error in form.email.errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Profile Picture -->
      <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg">
        <h3 class="text-xl mb-4 border-b border-[var(--bg-card-alt)] pb-2">
          <i class="fa-solid fa-image mr-2"></i>Profile Picture
        </h3>
        <div class="w-full">
          {{ form.picture.label(class_='block mb-2 text-sm') }}
          <div class="file-input-container">
            {{ form.picture(class_='hidden-file-input', id='profile-pic-input') }}
            <div class="file-input-button">
              <i class="fa-solid fa-upload"></i>
              <span class="file-input-text ml-2">Choose File</span>
            </div>
            <div id="file-name-display" class="file-name-display"></div>
          </div>
          {% if form.picture.errors %}
            <div class="text-red-500 text-xs mt-1">
              {% for error in form.picture.errors %}
                {{ error }}
              {% endfor %}
            </div>
          {% endif %}
        </div>
        <p class="text-xs text-[var(--text-secondary)] mt-2">Supported formats: JPG, PNG, JPEG</p>
        
        {% if user.profile_pic %}
        <div class="mt-4 text-center">
          <form method="POST" action="{{ url_for('auth.delete_profile_pic') }}">
            <button type="submit" class="text-red-500 hover:text-red-600 text-sm flex items-center justify-center mx-auto" onclick="return confirm('Are you sure you want to delete your profile picture?');">
              <i class="fa-solid fa-trash-can mr-1"></i> Remove Current Picture
            </button>
          </form>
        </div>
        {% endif %}
      </div>

      <!-- Password Update -->
      <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg md:col-span-2 xl:col-span-1">
        <h3 class="text-xl mb-4 border-b border-[var(--bg-card-alt)] pb-2">
          <i class="fa-solid fa-key mr-2"></i>Change Password <span class="text-xs text-[var(--text-secondary)]">(Optional)</span>
        </h3>
        <div class="grid grid-cols-1 gap-4">
          <div class="w-full">
            {{ form.password.label(class_='block mb-2 text-sm') }}
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]">
                <i class="fa-solid fa-lock"></i>
              </span>
              {{ form.password(class_='w-full pl-10', placeholder='Leave blank to keep current password') }}
              <button type="button" class="password-toggle-btn" aria-label="Toggle password visibility">
                <i class="fa-solid fa-eye"></i>
              </button>
            </div>
            {% if form.password.errors %}
              <div class="text-red-500 text-xs mt-1">
                {% for error in form.password.errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
          <div class="w-full">
            {{ form.confirm_password.label(class_='block mb-2 text-sm') }}
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]">
                <i class="fa-solid fa-shield-halved"></i>
              </span>
              {{ form.confirm_password(class_='w-full pl-10', placeholder='Confirm new password if changing') }}
              <button type="button" class="password-toggle-btn" aria-label="Toggle password visibility">
                <i class="fa-solid fa-eye"></i>
              </button>
            </div>
            {% if form.confirm_password.errors %}
              <div class="text-red-500 text-xs mt-1">
                {% for error in form.confirm_password.errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-center mt-8">
      <button type="submit" class="px-8 py-3 text-lg">
        <i class="fa-solid fa-floppy-disk mr-2"></i>Update Profile
      </button>
    </div>
  </form>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // File input styling
    const fileInput = document.getElementById('profile-pic-input');
    const fileNameDisplay = document.getElementById('file-name-display');
    
    if (fileInput) {
      fileInput.addEventListener('change', function() {
        if (this.files && this.files.length > 0) {
          fileNameDisplay.textContent = this.files[0].name;
          fileNameDisplay.style.display = 'block';
        } else {
          fileNameDisplay.textContent = '';
          fileNameDisplay.style.display = 'none';
        }
      });
    }
  });
</script>
{% endblock %}
