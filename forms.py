from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, SubmitField, BooleanField, FileField, TextAreaField, SelectField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional
from flask_wtf.file import FileAllowed
from models import User

class RegisterForm(FlaskForm):
    username = StringField("Username", validators=[DataRequired(), Length(min=3)])
    email = StringField("Email", validators=[DataRequired(), Email()])
    password = PasswordField("Password", validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField("Confirm Password", validators=[EqualTo('password')])
    submit = SubmitField("Register")

    def validate_username(self, username):
        if User.query.filter_by(username=username.data).first():
            raise ValidationError("Username already exists.")

    def validate_email(self, email):
        if User.query.filter_by(email=email.data).first():
            raise ValidationError("Email already exists.")

class LoginForm(FlaskForm):
    email = StringField("Email", validators=[DataRequired(), Email()])
    password = PasswordField("Password", validators=[DataRequired()])
    remember = BooleanField("Remember Me")
    submit = SubmitField("Login")

class RequestResetForm(FlaskForm):
    email = StringField("Email", validators=[DataRequired(), Email()])
    submit = SubmitField("Request Password Reset")

    def validate_email(self, email):
        if not User.query.filter_by(email=email.data).first():
            raise ValidationError("No account with that email.")

class ResetPasswordForm(FlaskForm):
    password = PasswordField("New Password", validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField("Confirm Password", validators=[EqualTo('password')])
    submit = SubmitField("Reset Password")

class ProfileUpdateForm(FlaskForm):
    username = StringField("Username", validators=[DataRequired(), Length(min=3)])
    email = StringField("Email", validators=[DataRequired(), Email()])
    picture = FileField("Update Profile Picture", validators=[FileAllowed(['jpg', 'png', 'jpeg'])])
    password = PasswordField("New Password", validators=[
        Optional(),
        Length(min=6, message="Password must be at least 6 characters.")
    ])
    confirm_password = PasswordField("Confirm New Password", validators=[
        Optional(),
        EqualTo('password', message="Passwords must match.")
    ])
    submit = SubmitField("Update Profile")

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user and user.id != getattr(self, 'user_id', None):
            raise ValidationError("Username already exists.")

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user and user.id != getattr(self, 'user_id', None):
            raise ValidationError("Email already exists.")
            
    def validate_confirm_password(self, confirm_password):
        if self.password.data and not confirm_password.data:
            raise ValidationError("Please confirm your password.")


class CodeSnippetForm(FlaskForm):
    title = StringField("Title", validators=[DataRequired(), Length(min=1, max=100)])
    description = TextAreaField("Description", validators=[Optional(), Length(max=500)])
    code = TextAreaField("Code", validators=[DataRequired()], render_kw={"rows": 15, "placeholder": "Enter your code here..."})
    language = SelectField("Language", choices=[
        ('text', 'Plain Text'),
        ('python', 'Python'),
        ('javascript', 'JavaScript'),
        ('java', 'Java'),
        ('cpp', 'C++'),
        ('c', 'C'),
        ('csharp', 'C#'),
        ('php', 'PHP'),
        ('ruby', 'Ruby'),
        ('go', 'Go'),
        ('rust', 'Rust'),
        ('swift', 'Swift'),
        ('kotlin', 'Kotlin'),
        ('typescript', 'TypeScript'),
        ('html', 'HTML'),
        ('css', 'CSS'),
        ('sql', 'SQL'),
        ('bash', 'Bash/Shell'),
        ('json', 'JSON'),
        ('xml', 'XML'),
        ('yaml', 'YAML'),
        ('markdown', 'Markdown')
    ], default='text')
    is_public = BooleanField("Make this snippet public", default=True)
    submit = SubmitField("Save Snippet")


class EditSnippetForm(CodeSnippetForm):
    submit = SubmitField("Update Snippet")
