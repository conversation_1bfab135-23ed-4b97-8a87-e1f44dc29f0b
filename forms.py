from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField, BooleanField, FileField
from wtforms.validators import <PERSON>Required, Email, EqualTo, Length, ValidationError, Optional
from flask_wtf.file import FileAllowed
from models import User

class RegisterForm(FlaskForm):
    username = String<PERSON>ield("Username", validators=[DataRequired(), Length(min=3)])
    email = StringField("Email", validators=[DataRequired(), Email()])
    password = PasswordField("Password", validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField("Confirm Password", validators=[EqualTo('password')])
    submit = SubmitField("Register")

    def validate_username(self, username):
        if User.query.filter_by(username=username.data).first():
            raise ValidationError("Username already exists.")

    def validate_email(self, email):
        if User.query.filter_by(email=email.data).first():
            raise ValidationError("Email already exists.")

class LoginForm(FlaskForm):
    email = StringField("Email", validators=[DataRequired(), Email()])
    password = PasswordField("Password", validators=[DataRequired()])
    remember = BooleanField("Remember Me")
    submit = SubmitField("Login")

class RequestResetForm(FlaskForm):
    email = StringField("Email", validators=[DataRequired(), Email()])
    submit = SubmitField("Request Password Reset")

    def validate_email(self, email):
        if not User.query.filter_by(email=email.data).first():
            raise ValidationError("No account with that email.")

class ResetPasswordForm(FlaskForm):
    password = PasswordField("New Password", validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField("Confirm Password", validators=[EqualTo('password')])
    submit = SubmitField("Reset Password")

class ProfileUpdateForm(FlaskForm):
    username = StringField("Username", validators=[DataRequired(), Length(min=3)])
    email = StringField("Email", validators=[DataRequired(), Email()])
    picture = FileField("Update Profile Picture", validators=[FileAllowed(['jpg', 'png', 'jpeg'])])
    password = PasswordField("New Password", validators=[
        Optional(),
        Length(min=6, message="Password must be at least 6 characters.")
    ])
    confirm_password = PasswordField("Confirm New Password", validators=[
        Optional(),
        EqualTo('password', message="Passwords must match.")
    ])
    submit = SubmitField("Update Profile")

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user and user.id != getattr(self, 'user_id', None):
            raise ValidationError("Username already exists.")

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user and user.id != getattr(self, 'user_id', None):
            raise ValidationError("Email already exists.")
            
    def validate_confirm_password(self, confirm_password):
        if self.password.data and not confirm_password.data:
            raise ValidationError("Please confirm your password.")
