{% extends "layout.html" %}

{% block title %}Create Snippet - Flask Auth{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center mb-6">
    <a href="{{ url_for('auth.snippets') }}" 
       class="text-[var(--accent-blue)] hover:underline mr-4">
      <i class="fa-solid fa-arrow-left mr-2"></i>Back to Snippets
    </a>
    <h1 class="text-3xl font-bold custom-logo">
      <i class="fa-solid fa-plus mr-3"></i>Create Code Snippet
    </h1>
  </div>

  <form method="POST" class="space-y-6">
    {{ form.hidden_tag() }}
    
    <div class="grid md:grid-cols-2 gap-6">
      <!-- Title -->
      <div>
        {{ form.title.label(class="block text-sm font-medium mb-2") }}
        {{ form.title(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent-blue)] focus:border-transparent") }}
        {% if form.title.errors %}
          <div class="mt-1 text-sm text-red-600">
            {% for error in form.title.errors %}
              <p>{{ error }}</p>
            {% endfor %}
          </div>
        {% endif %}
      </div>

      <!-- Language -->
      <div>
        {{ form.language.label(class="block text-sm font-medium mb-2") }}
        {{ form.language(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent-blue)] focus:border-transparent") }}
        {% if form.language.errors %}
          <div class="mt-1 text-sm text-red-600">
            {% for error in form.language.errors %}
              <p>{{ error }}</p>
            {% endfor %}
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Description -->
    <div>
      {{ form.description.label(class="block text-sm font-medium mb-2") }}
      {{ form.description(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent-blue)] focus:border-transparent", rows="3", placeholder="Optional description of your code snippet...") }}
      {% if form.description.errors %}
        <div class="mt-1 text-sm text-red-600">
          {% for error in form.description.errors %}
            <p>{{ error }}</p>
          {% endfor %}
        </div>
      {% endif %}
    </div>

    <!-- Code -->
    <div>
      {{ form.code.label(class="block text-sm font-medium mb-2") }}
      {{ form.code(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent-blue)] focus:border-transparent font-mono text-sm", style="resize: vertical; min-height: 300px;") }}
      {% if form.code.errors %}
        <div class="mt-1 text-sm text-red-600">
          {% for error in form.code.errors %}
            <p>{{ error }}</p>
          {% endfor %}
        </div>
      {% endif %}
    </div>

    <!-- Visibility -->
    <div class="flex items-center space-x-3">
      {{ form.is_public(class="h-4 w-4 text-[var(--accent-blue)] focus:ring-[var(--accent-blue)] border-gray-300 rounded") }}
      {{ form.is_public.label(class="text-sm font-medium") }}
      <span class="text-xs text-gray-500">
        (Public snippets can be viewed by anyone, private snippets are only visible to you)
      </span>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end space-x-4">
      <a href="{{ url_for('auth.snippets') }}" 
         class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
        Cancel
      </a>
      {{ form.submit(class="custom-btn-primary px-6 py-2 rounded-lg") }}
    </div>
  </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Auto-resize textarea
  const codeTextarea = document.querySelector('textarea[name="code"]');
  if (codeTextarea) {
    codeTextarea.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = Math.max(300, this.scrollHeight) + 'px';
    });
  }
  
  // Add tab support to code textarea
  codeTextarea.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = this.selectionStart;
      const end = this.selectionEnd;
      
      // Insert tab character
      this.value = this.value.substring(0, start) + '\t' + this.value.substring(end);
      
      // Move cursor
      this.selectionStart = this.selectionEnd = start + 1;
    }
  });
});
</script>
{% endblock %}
