# Flask Code Snippet Sharing App

A modern Flask web application for sharing and managing code snippets with user authentication and profile management.

## 🚀 Features

### 👤 User Management
- User registration and authentication
- Profile management with picture upload
- Password reset functionality
- Secure session management

### 📝 Code Snippet Sharing
- Create, edit, and delete code snippets
- Support for 22+ programming languages
- Syntax highlighting with Prism.js
- Public/private visibility settings
- Browse community snippets
- Personal snippet collections

### 🎨 Modern UI
- Responsive design with Tailwind CSS
- Dark theme with gradient accents
- Collapsible sidebar navigation
- Interactive code editor with tab support
- Copy-to-clipboard functionality

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd elib
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run database migration (if needed)**
   ```bash
   python migrate_db.py
   ```

4. **Start the application**
   ```bash
   python app.py
   ```

5. **Open your browser**
   Navigate to `http://localhost:5000`

## 📋 Dependencies

- Flask 2.3.3
- Flask-SQLAlchemy 3.0.5
- Flask-Login 0.6.3
- Flask-Bcrypt 1.0.1
- Flask-WTF 1.1.1
- WTForms 3.0.1
- Pillow 10.0.1 (for image processing)

## 🗂️ Project Structure

```
elib/
├── app.py              # Application factory
├── config.py           # Configuration settings
├── extensions.py       # Flask extensions
├── models.py           # Database models
├── forms.py            # WTForms definitions
├── routes.py           # Application routes
├── migrate_db.py       # Database migration script
├── requirements.txt    # Python dependencies
├── static/
│   ├── style.css       # Custom styles
│   └── profile_pics/   # User profile pictures
└── templates/
    ├── layout.html     # Base template
    ├── *.html          # Page templates
    └── snippets/       # Snippet-related templates
```

## 🔧 Usage

### Creating Code Snippets
1. Register/login to your account
2. Click "New Snippet" in the sidebar
3. Fill in title, description, and code
4. Select programming language
5. Choose visibility (public/private)
6. Save your snippet

### Managing Profile
1. Go to Dashboard
2. Update username, email, or password
3. Upload profile picture (auto-resized to 300x300)
4. Remove profile picture to show initials

### Browsing Snippets
- **Code Snippets**: View all public snippets + your own
- **My Snippets**: Personal collection management
- Click any snippet to view with syntax highlighting
- Copy code with one-click functionality

## 🔒 Security Features

- Password hashing with bcrypt
- CSRF protection on all forms
- File upload validation and sanitization
- User authorization for snippet operations
- Secure session management

## 🎯 Database Schema

### User Model
- id, username, email, password
- profile_pic (nullable)
- Relationship with code snippets

### CodeSnippet Model
- id, title, description, code
- language, is_public
- created_at, updated_at
- user_id (foreign key)

## 🚨 Troubleshooting

If you encounter database issues:
1. Run `python migrate_db.py` to fix schema
2. Delete `instance/site.db` for fresh start
3. Restart the application

## 📝 License

This project is open source and available under the MIT License.
