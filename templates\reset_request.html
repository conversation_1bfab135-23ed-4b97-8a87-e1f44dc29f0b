{% extends "layout.html" %}
{% block title %}Reset Password{% endblock %}
{% block content %}
<div class="w-full">
  <h2 class="text-3xl font-semibold mb-8 text-center">Request Password Reset</h2>

  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 md:gap-6">
    <div class="md:col-span-1 bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg flex flex-col items-center justify-center">
      <div class="text-4xl md:text-5xl mb-4 text-transparent bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] bg-clip-text">
        <i class="fa-solid fa-unlock-keyhole"></i>
      </div>
      <h3 class="text-xl mb-2 text-center">Forgot Password?</h3>
      <p class="text-[var(--text-secondary)] text-sm text-center">
        We'll send you a reset link to get back into your account.
      </p>
    </div>

    <div class="md:col-span-3 bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg">
      <form method="POST" class="space-y-4 md:space-y-6 w-full">
        {{ form.hidden_tag() }}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div class="lg:col-span-2 w-full">
            {{ form.email.label(class_='block mb-1 md:mb-2 text-sm') }}
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]">
                <i class="fa-solid fa-envelope"></i>
              </span>
              {{ form.email(class_='w-full pl-10', placeholder='Enter your email address') }}
            </div>
            <p class="text-xs text-[var(--text-secondary)] mt-2">
              Enter your email address to receive a password reset link
            </p>
          </div>
        </div>

        <div class="mt-6 md:mt-8">
          <button type="submit" class="w-full flex items-center justify-center">
            <i class="fa-solid fa-paper-plane mr-2"></i>
            <span>Send Reset Link</span>
          </button>
        </div>

        <div class="mt-4 md:mt-6 text-center text-sm">
          <a href="{{ url_for('auth.login') }}" class="custom-link">
            <i class="fa-solid fa-arrow-left mr-1"></i> Back to Login
          </a>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
