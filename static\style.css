:root {
  --bg-base:    #121212;
  --bg-card:    #1E1E1E;
  --bg-card-alt:#2A2A2A;
  --text-primary:  #E0E0E0;
  --text-secondary:#A0A0A0;
  --accent-purple: #8A2BE2;
  --accent-blue:   #3B82F6;
  --hover-purple:  #9B4DFF;
  --hover-blue:    #60A5FA;
}

body.custom-bg {
   background: linear-gradient(135deg, var(--bg-base) 0%, #1a1a24 100%);
   color: var(--text-primary);
   font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Sidebar styles */
#sidebar {
   width: 16rem;
   min-width: 4rem;
   max-width: 100vw;
   transition: width 0.3s, left 0.3s, box-shadow 0.3s;
   z-index: 40;
   background-color: var(--bg-card);
   box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}

.sidebar-collapsed {
   width: 0 !important;
   min-width: 0 !important;
   overflow: visible !important;
}

.sidebar-collapsed > * {
   display: none !important;
}

.sidebar-collapsed #sidebar-toggle {
   display: flex !important;
   position: absolute;
   left: 0.5rem;
   transform: translateX(0);
   z-index: 100;
}

.sidebar-expanded {
   width: 16rem !important;
}

.sidebar-link {
   position: relative;
   overflow: hidden;
}

.sidebar-link::after {
   content: '';
   position: absolute;
   left: 0;
   bottom: 0;
   height: 2px;
   width: 0;
   background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
   transition: width 0.3s ease;
}

.sidebar-link:hover::after {
   width: 100%;
}

.sidebar-link span {
   transition: opacity 0.2s;
}

.sidebar-collapsed .sidebar-link span {
   opacity: 0;
   pointer-events: none;
}

.sidebar-collapsed .sidebar-logo-text {
   display: none;
}

.sidebar-expanded .sidebar-logo-text {
   display: inline;
}

@media (max-width: 767px) {
   #sidebar {
      position: fixed;
      left: -16rem;
      top: 0;
      height: 100vh;
      box-shadow: 2px 0 16px #000a;
   }

   #sidebar.sidebar-expanded {
      left: 0;
   }

   #sidebar.sidebar-collapsed {
      left: -16rem;
   }

   #main-content-wrapper {
      margin-left: 0 !important;
      width: 100% !important;
   }

   .sidebar-overlay {
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 30;
   }
   
   /* Responsive grid adjustments for mobile */
   .grid {
      grid-template-columns: 1fr !important;
   }
   
   .md\:col-span-1,
   .md\:col-span-2,
   .md\:col-span-3 {
      grid-column: span 1 !important;
   }
}

@media (min-width: 768px) and (max-width: 1023px) {
   /* Tablet adjustments */
   .grid {
      gap: 1rem !important;
   }
}

@media (min-width: 768px) {
   #sidebar {
      position: fixed;
      left: 0;
      top: 0;
      height: 100vh;
   }

   #main-content-wrapper {
      margin-left: 16rem;
      transition: margin-left 0.3s;
   }

   #sidebar.sidebar-collapsed ~ #main-content-wrapper {
      margin-left: 0 !important;
      width: 100% !important;
   }
}

/* Main content styles */
#main-content {
   padding: 1rem;
   width: 100%;
   overflow-x: hidden;
}

#main-content > div {
   width: 100%;
   margin: 0 auto;
}

@media (min-width: 1536px) {
   #main-content {
      padding: 2rem;
   }
}

/* Other custom styles */
.custom-header {
   background: var(--bg-card);
   border-bottom: 2px solid;
   border-image: linear-gradient(90deg, var(--accent-blue), var(--accent-purple)) 1;
   box-shadow: 0 2px 8px 0 #000a;
}

.custom-logo {
   color: #fff;
   background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
   -webkit-background-clip: text;
   background-clip: text;
   -webkit-text-fill-color: transparent;
   letter-spacing: 1px;
   font-weight: 700;
}

.custom-link {
   color: var(--accent-blue);
   transition: color 0.2s;
   text-decoration: none;
   font-weight: 500;
}

.custom-link:hover {
   color: var(--accent-purple);
   text-shadow: 0 0 4px rgba(154, 77, 255, 0.3);
   text-decoration: underline;
}

.custom-flash {
   background: var(--bg-card-alt);
   border-left: 4px solid;
   border-image: linear-gradient(180deg, var(--accent-blue), var(--accent-purple)) 1;
   border-radius: 0.375rem;
   padding: 0.5rem 1rem;
   color: var(--text-primary);
   font-size: 0.95rem;
   box-shadow: 0 2px 8px 0 #0006;
}

.custom-footer {
   background: var(--bg-card);
   color: var(--text-secondary);
   border-top: 1px solid var(--bg-card-alt);
}

.card {
   background: var(--bg-card-alt);
   border-radius: 0.75rem;
   box-shadow: 0 4px 24px 0 #000a;
   padding: 2rem 2rem 1.5rem 2rem;
   border: 1px solid #333340;
}

h1, h2, h3, h4, h5, h6 {
   font-family: 'Poppins', 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
   background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
   -webkit-background-clip: text;
   background-clip: text;
   -webkit-text-fill-color: transparent;
   letter-spacing: 0.5px;
   font-weight: 600;
}

/* Form element styles */
.relative {
   position: relative;
   width: 100%;
   overflow: hidden;
}

input,
textarea,
select {
   background-color: var(--bg-card);
   color: var(--text-primary);
   padding: 0.5rem;
   border-radius: 0.375rem;
   border: 1px solid #374151;
   width: 100%;
   font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
   transition: border 0.2s, box-shadow 0.2s;
   box-sizing: border-box;
   max-width: 100%;
}

input:focus,
textarea:focus {
   outline: none;
   border-color: var(--accent-blue);
   box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
}

/* Checkbox styling */
input[type="checkbox"] {
   appearance: none;
   -webkit-appearance: none;
   width: 1.25rem;
   height: 1.25rem;
   background-color: var(--bg-card);
   border: 1px solid #374151;
   border-radius: 0.25rem;
   cursor: pointer;
   display: inline-flex;
   align-items: center;
   justify-content: center;
   position: relative;
   margin-right: 0.5rem;
   vertical-align: middle;
}

input[type="checkbox"]:checked {
   background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
   border-color: var(--accent-blue);
}

input[type="checkbox"]:checked::after {
   content: '\2714';
   color: #fff;
   font-size: 0.75rem;
   font-weight: bold;
   position: absolute;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
}

input[type="checkbox"]:focus {
   border-color: var(--accent-blue);
   box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
}

button {
   background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
   color: #fff;
   font-weight: bold;
   padding: 0.5rem 1rem;
   border-radius: 0.375rem;
   border: none;
   font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
   box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.5);
   transition: transform 0.2s, box-shadow 0.2s;
}

button:hover {
   background: linear-gradient(90deg, var(--hover-blue), var(--hover-purple));
   box-shadow: 0 4px 16px 0 rgba(155, 77, 255, 0.5);
   transform: translateY(-1px);
}

/* Fix input field overflow */
.grid {
   width: 100%;
   box-sizing: border-box;
}

.grid > div {
   width: 100%;
   box-sizing: border-box;
}

::-webkit-scrollbar {
   width: 8px;
   background: var(--bg-card);
}

::-webkit-scrollbar-thumb {
   background: linear-gradient(180deg, var(--accent-blue), var(--accent-purple));
   border-radius: 4px;
}

/* Responsive form adjustments */
@media (max-width: 640px) {
   .space-y-4 > div,
   .space-y-6 > div {
      margin-bottom: 1rem;
   }
   
   .p-4, .p-6, .p-8 {
      padding: 1rem !important;
   }
   
   h2.text-3xl {
      font-size: 1.5rem;
   }
   
   .text-5xl {
      font-size: 3rem;
   }
   
   /* Fix input overflow on small screens */
   input, textarea, select {
      font-size: 14px;
   }
}

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* Password toggle button */
.password-toggle-btn {
   position: absolute;
   right: 0.75rem;
   top: 50%;
   transform: translateY(-50%);
   background: transparent;
   border: none;
   color: var(--accent-blue);
   cursor: pointer;
   display: flex;
   align-items: center;
   justify-content: center;
   padding: 0;
   width: 1.5rem;
   height: 1.5rem;
   box-shadow: none;
   transition: color 0.2s;
   z-index: 10;
}

.password-toggle-btn:hover {
   color: var(--accent-purple);
   background: transparent;
   box-shadow: none;
   transform: translateY(-50%);
}

.password-toggle-btn:focus {
   outline: none;
}

/* Adjust password field padding to accommodate the toggle button */
input[type="password"] {
   padding-right: 2.5rem;
}

/* Custom file input styling */
.file-input-container {
   position: relative;
   width: 100%;
   overflow: hidden;
}

.file-input-container input[type="file"] {
   position: absolute;
   left: 0;
   top: 0;
   opacity: 0;
   width: 100%;
   height: 100%;
   cursor: pointer;
   z-index: 10;
}

.file-input-button {
   display: flex;
   align-items: center;
   justify-content: center;
   background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
   color: #fff;
   padding: 0.5rem 1rem;
   border-radius: 0.375rem;
   font-weight: 500;
   width: 100%;
   transition: all 0.2s;
   border: none;
   box-shadow: 0 2px 8px 0 rgba(59, 130, 246, 0.3);
}

.file-input-button:hover {
   background: linear-gradient(90deg, var(--hover-blue), var(--hover-purple));
   box-shadow: 0 4px 16px 0 rgba(155, 77, 255, 0.3);
   transform: translateY(-1px);
}

.file-input-text {
   margin-left: 0.5rem;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
}

.file-name-display {
   margin-top: 0.5rem;
   font-size: 0.875rem;
   color: var(--accent-blue);
   text-align: center;
   word-break: break-all;
   display: none;
}