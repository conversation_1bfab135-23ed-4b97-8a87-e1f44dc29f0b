<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{% block title %}Flask Auth{% endblock %}</title>

    <!-- Tailwind CDN for responsiveness only -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        corePlugins: { preflight: false },
        theme: { extend: {} },
      };
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS for colors and effects -->
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}" />

    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.css" rel="stylesheet" />
  </head>
  <body class="custom-bg text-custom min-h-screen flex overflow-x-hidden">
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar-expanded custom-header flex flex-col h-screen transition-all duration-300 z-40 fixed">
      <!-- Toggle Button -->
      <button
        id="sidebar-toggle"
        class="custom-link text-2xl flex items-center justify-center absolute -right-4 top-16 transform z-50 bg-[var(--bg-card)] rounded-full shadow-lg w-8 h-8 border border-[var(--accent-blue)] hover:scale-110 hover:shadow-[var(--accent-purple)]/50 transition-all duration-200"
        aria-label="Toggle sidebar"
        style="box-shadow: 0 0 8px rgba(59, 130, 246, 0.5)"
      >
        <span id="sidebar-arrow" class="text-[var(--accent-blue)]" style="text-shadow: 0 0 4px rgba(59, 130, 246, 0.5)">
          <i class="fa-solid fa-chevron-left"></i>
        </span>
      </button>

      <!-- Sidebar Header -->
      <div class="flex items-center justify-center py-6 border-b border-[var(--bg-card-alt)]">
        <span class="text-xl font-bold custom-logo flex items-center">
          <i class="fa-solid fa-lock"></i>
          <span class="sidebar-logo-text ml-2">Flask Auth</span>
        </span>
      </div>

      <!-- Navigation -->
      <nav class="flex-1 overflow-y-auto py-4 px-3">
        <div class="space-y-1">
          <a
            href="{{ url_for('auth.home') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-house text-lg mr-3"></i><span>Home</span>
          </a>
          <a
            href="{{ url_for('auth.snippets') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-code text-lg mr-3"></i><span>Code Snippets</span>
          </a>
          {% if current_user.is_authenticated %}
          <a
            href="{{ url_for('auth.dashboard') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-user text-lg mr-3"></i><span>Dashboard</span>
          </a>
          <a
            href="{{ url_for('auth.my_snippets') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-file-code text-lg mr-3"></i><span>My Snippets</span>
          </a>
          <a
            href="{{ url_for('auth.create_snippet') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-plus text-lg mr-3"></i><span>New Snippet</span>
          </a>
          <a
            href="{{ url_for('auth.logout') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-right-from-bracket text-lg mr-3"></i><span>Logout</span>
          </a>
          {% else %}
          <a
            href="{{ url_for('auth.login') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-key text-lg mr-3"></i><span>Login</span>
          </a>
          <a
            href="{{ url_for('auth.register') }}"
            class="sidebar-link custom-link flex items-center px-3 py-2 rounded-lg hover:bg-[var(--bg-card-alt)] transition-colors"
          >
            <i class="fa-solid fa-user-plus text-lg mr-3"></i><span>Register</span>
          </a>
          {% endif %}
        </div>
      </nav>

      <!-- Sidebar Footer -->
      <div class="p-4 text-xs custom-footer text-center border-t border-[var(--bg-card-alt)]">
        <span><i class="fa-solid fa-code"></i> Made for devs</span><br />
        &copy; 2025 Flask Auth App
      </div>
    </div>

    <!-- Overlay for mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay hidden md:hidden"></div>

    <!-- Main Content -->
    <div id="main-content-wrapper" class="transition-all duration-300 min-h-screen flex-1 flex flex-col">
      <main id="main-content" class="flex-1 p-4 md:p-6 lg:p-8 flex flex-col items-center">
        <div class="w-full max-w-full md:max-w-4xl lg:max-w-6xl xl:max-w-7xl 2xl:max-w-[90%]">
          <!-- Flash Messages -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              <div class="mb-6 space-y-2">
                {% for category, message in messages %}
                  <div class="custom-flash">{{ message }}</div>
                {% endfor %}
              </div>
            {% endif %}
          {% endwith %}
          
          <!-- Main Content -->
          <div class="bg-[var(--bg-card-alt)]/90 rounded-xl shadow-2xl p-4 md:p-6 lg:p-8" style="box-shadow: 0 8px 32px 0 rgba(59, 130, 246, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)">
            {% block content %}{% endblock %}
          </div>
        </div>
      </main>
    </div>

    <script>
      // Sidebar toggle for mobile and collapse/expand
      const sidebar = document.getElementById("sidebar");
      const mainContentWrapper = document.getElementById("main-content-wrapper");
      const toggleBtn = document.getElementById("sidebar-toggle");
      const overlay = document.getElementById("sidebar-overlay");
      const arrow = document.getElementById("sidebar-arrow");
      let collapsed = false;

      function setSidebarState() {
        if (window.innerWidth < 768) {
          // Mobile
          sidebar.classList.remove("sidebar-collapsed");
          sidebar.classList.add("sidebar-expanded");
          sidebar.style.left = "-16rem";
          overlay.classList.add("hidden");
          mainContentWrapper.style.marginLeft = "0";
        } else {
          // Desktop
          overlay.classList.add("hidden");
          sidebar.classList.remove("sidebar-expanded");
          sidebar.classList.remove("sidebar-collapsed");
          sidebar.classList.add(collapsed ? "sidebar-collapsed" : "sidebar-expanded");
          sidebar.style.left = "0";
          mainContentWrapper.style.marginLeft = collapsed ? "0" : "16rem";
        }
        
        // Update arrow direction
        if (arrow) {
          arrow.innerHTML = collapsed ? '<i class="fa-solid fa-chevron-right"></i>' : '<i class="fa-solid fa-chevron-left"></i>';
        }
      }

      if (toggleBtn) {
        toggleBtn.addEventListener("click", () => {
          if (window.innerWidth < 768) {
            // Mobile: open sidebar overlay
            sidebar.classList.add("sidebar-expanded");
            sidebar.classList.remove("sidebar-collapsed");
            sidebar.style.left = "0";
            overlay.classList.remove("hidden");
            document.body.style.overflow = "hidden";
          } else {
            // Desktop: collapse/expand
            collapsed = !collapsed;
            sidebar.classList.toggle("sidebar-collapsed", collapsed);
            sidebar.classList.toggle("sidebar-expanded", !collapsed);
            mainContentWrapper.style.marginLeft = collapsed ? "0" : "16rem";
            if (arrow) {
              arrow.innerHTML = collapsed ? '<i class="fa-solid fa-chevron-right"></i>' : '<i class="fa-solid fa-chevron-left"></i>';
            }
          }
        });
      }

      if (overlay) {
        overlay.addEventListener("click", () => {
          sidebar.classList.remove("sidebar-expanded");
          sidebar.classList.add("sidebar-collapsed");
          sidebar.style.left = "-16rem";
          overlay.classList.add("hidden");
          document.body.style.overflow = "";
        });
      }

      window.addEventListener("resize", setSidebarState);
      setSidebarState();
      
      // Password toggle functionality
      document.addEventListener('DOMContentLoaded', function() {
        const toggleButtons = document.querySelectorAll('.password-toggle-btn');
        
        toggleButtons.forEach(button => {
          button.addEventListener('click', function() {
            // Find the password input field
            const parentDiv = this.closest('.relative');
            const passwordField = parentDiv.querySelector('input[type="password"], input[type="text"]');
            const icon = this.querySelector('i');
            
            if (passwordField) {
              if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
              } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
              }
            }
          });
        });
      });
    </script>

    <!-- Prism.js for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>

    <script>
      // Initialize Prism.js after page load
      document.addEventListener('DOMContentLoaded', function() {
        if (typeof Prism !== 'undefined') {
          Prism.highlightAll();
        }
      });
    </script>
  </body>
</html>
